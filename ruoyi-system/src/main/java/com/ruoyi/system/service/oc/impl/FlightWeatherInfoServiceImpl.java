package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.dto.FlightDynamicInfoDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDynamicDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherInfoDto;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import com.ruoyi.system.service.oc.IFlightWeatherInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * yd
 */
@Service
@Slf4j
public class FlightWeatherInfoServiceImpl extends ServiceImpl<FlightWeatherInfoMapper, FlightWeatherInfo> implements IFlightWeatherInfoService {
    @Resource
    private FlightWeatherInfoMapper flightWeatherInfoBizMapper;
    @Resource
    private FlightDynamicInfoMapper flightDynamicInfoMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;
    @Resource
    private IFlightWeatherInfoService iFlightWeatherInfoService;
    @Resource
    private IFlightDynamicInfoService iFlightDynamicInfoService;

    /**
     * 查询气象信息
     */
    @Override
    public FlightWeatherDynamicVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode) {
        FlightWeatherDynamicVo flightWeatherDynamicVo = flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
        if (flightWeatherDynamicVo != null) {
            //查询任务书信息查询动态信息详情
            List<FlightDynamicInfo> flightDynamicInfos = flightDynamicInfoMapper.selectFlightDynamicInfoByTaskBookNumber(flightWeatherDynamicVo.getTaskBookNumber(), companyCode);
            flightWeatherDynamicVo.setFlightDynamicInfoList(flightDynamicInfos);
        }
        return flightWeatherDynamicVo;
    }

    @Override
    public int insertFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto) {
        //查询任务书编号
        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDynamicDto);
        if (StrUtil.isNotEmpty(taskBookNumber)) {
            List<FlightWeatherInfoDto> flightWeatherInfoDtos = flightWeatherDynamicDto.getFlightWeatherInfoDtos();
            if (CollUtil.isNotEmpty(flightWeatherInfoDtos)) {
                //封装新增的数据 保存天气数据
                saveWeatherInfo(companyCode, flightWeatherInfoDtos, taskBookNumber);
            }
            //处理动态信息
            List<FlightDynamicInfoDto> flightDynamicInfoDtos = flightWeatherDynamicDto.getFlightDynamicInfoDtos();
            if (CollUtil.isNotEmpty(flightDynamicInfoDtos)) {
                saveDynamicInfo(companyCode, flightDynamicInfoDtos, taskBookNumber);
            }
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public int updateFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto) {
        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDynamicDto);
        if (StrUtil.isNotEmpty(taskBookNumber)) {
            List<FlightWeatherInfoDto> flightWeatherInfoDtos = flightWeatherDynamicDto.getFlightWeatherInfoDtos();
            if (CollUtil.isNotEmpty(flightWeatherInfoDtos)) {
                //先删除旧数据
                iFlightWeatherInfoService.lambdaUpdate().eq(FlightWeatherInfo::getTaskBookNumber, taskBookNumber).remove();
                //封装新增的数据
                saveWeatherInfo(companyCode, flightWeatherInfoDtos, taskBookNumber);
                //处理动态信息 先删除旧数据
                List<FlightDynamicInfoDto> flightDynamicInfoDtos = flightWeatherDynamicDto.getFlightDynamicInfoDtos();
                if (CollUtil.isNotEmpty(flightDynamicInfoDtos)) {
                    iFlightDynamicInfoService.lambdaUpdate().eq(FlightDynamicInfo::getTaskBookNumber, taskBookNumber).remove();
                    saveDynamicInfo(companyCode, flightDynamicInfoDtos, taskBookNumber);
                }
            }
            return 1;
        } else {
            return 0;
        }
    }

    private void saveWeatherInfo(String companyCode, List<FlightWeatherInfoDto> flightWeatherInfoDtos, String taskBookNumber) {
        List<FlightWeatherInfo> flightWeatherInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);
        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选taskInfoId和批次都匹配的数据
            List<FlightWeatherInfoDto> matchingWeatherDtos = new ArrayList<>();
            for (FlightWeatherInfoDto weatherDto : flightWeatherInfoDtos) {
                for (FlightTaskInfo taskInfo : flightTaskInfos) {
                    if (weatherDto.getFlightTaskInfoId() != null && weatherDto.getFlightTaskInfoId().equals(taskInfo.getId()) && StrUtil.equals(weatherDto.getBatch(), taskInfo.getBatch())) {
                        matchingWeatherDtos.add(weatherDto);
                        break; // 找到匹配项后退出内层循环
                    }
                }
            }
            // 更新为匹配后的数据列表
            flightWeatherInfoDtos = matchingWeatherDtos;
        }
        flightWeatherInfoDtos.forEach(dto -> {
            FlightWeatherInfo flightWeatherInfo = new FlightWeatherInfo();
            BeanUtils.copyProperties(dto, flightWeatherInfo);
            flightWeatherInfo.setTaskBookNumber(taskBookNumber);
            flightWeatherInfo.setCompanyCode(companyCode);
            flightWeatherInfos.add(flightWeatherInfo);
        });
        iFlightWeatherInfoService.saveBatch(flightWeatherInfos);
    }

    private void saveDynamicInfo(String companyCode, List<FlightDynamicInfoDto> flightDynamicInfoDtos, String taskBookNumber) {
        List<FlightDynamicInfo> flightDynamicInfos = new ArrayList<>();
        flightDynamicInfoDtos.forEach(dto -> {
            FlightDynamicInfo flightDynamicInfo = new FlightDynamicInfo();
            BeanUtils.copyProperties(dto, flightDynamicInfo);
            flightDynamicInfo.setTaskBookNumber(taskBookNumber);
            flightDynamicInfo.setCompanyCode(companyCode);
            flightDynamicInfos.add(flightDynamicInfo);
        });
        iFlightDynamicInfoService.saveBatch(flightDynamicInfos);
    }

    private String getFlightTaskBook(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(flightWeatherDynamicDto.getTaskBookId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            return dbTaskBook.getTaskBookNumber();
        }
        return StrUtil.EMPTY;
    }


    @Override
    public void exportWeatherInfoToWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        //查询对应数据
        FlightWeatherDynamicVo flightWeatherDynamicVo = this.selectFlightWeatherInfoById(flightTaskBookId.longValue(), companyCode);
        // 创建目标对象实例并拷贝属性
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherDynamicVo, wordMeteorologicalVo);

        try {
            // 生成Word文档
            byte[] documentBytes = this.generateWordDocument(wordMeteorologicalVo);

            // 设置响应头
            String fileName = "信息记录页_" + wordMeteorologicalVo.getFlightDate() + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setContentLength(documentBytes.length);

            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(documentBytes);
                outputStream.flush();
            }

        } catch (Exception e) {
            log.error("导出气象信息Word文档失败", e);
            throw new RuntimeException("导出Word文档失败", e);
        }
    }

    @Override
    public byte[] generateWordDocument(WordMeteorologicalVo wordMeteorologicalVo) {
        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 设置页面格式
            this.setPageFormat(document);
            // 创建完整的合并表格
            this.createCompleteTable(document, wordMeteorologicalVo);

            // 写入输出流
            document.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("生成Word文档失败", e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 创建完整的合并表格
     */
    private void createCompleteTable(XWPFDocument document, WordMeteorologicalVo exportData) {
        List<WordFlightWeatherInfoVo> departureWeatherInfoList = exportData.getDepartureWeatherInfoList();
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = exportData.getArrivalWeatherInfoList();
        List<WordFlightWeatherDynamicVo> dynamicInfoList = exportData.getDynamicInfoList();

        // 计算表格总行数：标题行(1) + 基本信息行(1) + 气象信息标题行(1) + 始发地表头行(1) + 始发地数据行 + 目的地表头行(1) + 目的地数据行
        // + 动态信息标题行(1) + 动态信息表头行(1) + 动态信息数据行(dynamicInfoList.size()) + 合计行(1)
        int departureWeatherDataRows = (departureWeatherInfoList != null) ? departureWeatherInfoList.size() : 0;
        int arrivalWeatherDataRows = (arrivalWeatherInfoList != null) ? arrivalWeatherInfoList.size() : 0;
        int dynamicDataRows = (dynamicInfoList != null) ? dynamicInfoList.size() : 0;
        int totalRows = 1 + 1 + 1 + 1 + departureWeatherDataRows + 1 + arrivalWeatherDataRows + 1 + 1 + dynamicDataRows + 1;

        // 创建表格，使用11列（动态信息表格的列数最多）
        XWPFTable table = document.createTable(totalRows, 11);
        table.setWidth("100%");
        this.setTableBorders(table);

        int currentRow = 0;

        // 第1行：标题行 - "信息记录页"
        XWPFTableRow titleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, 0, 0, 10); // 合并第一行的所有列
        this.setCellText(titleRow.getCell(0), "信息记录页", true, 10.5);

        // 第2行：基本信息行
        XWPFTableRow basicInfoRow = table.getRow(currentRow++);

        // 机型：合并第1列和第2列
        this.mergeCellsHorizontally(table, currentRow - 1, 0, 1);
        this.setCellText(basicInfoRow.getCell(0), "机型", true);

        // 机型数据：合并第3列和第4列
        this.mergeCellsHorizontally(table, currentRow - 1, 2, 3);
        this.setCellText(basicInfoRow.getCell(2), exportData.getAircraftType(), false);

        // 注册号：合并第5列和第6列
        this.mergeCellsHorizontally(table, currentRow - 1, 4, 5);
        this.setCellText(basicInfoRow.getCell(4), "注册号", true);

        // 注册号数据：合并第7列和第8列
        this.mergeCellsHorizontally(table, currentRow - 1, 6, 7);
        this.setCellText(basicInfoRow.getCell(6), exportData.getRegistrationNumber(), false);

        // 飞行日期：第9列
        this.setCellText(basicInfoRow.getCell(8), "飞行日期", true);

        // 飞行日期数据：合并第10列和第11列
        this.mergeCellsHorizontally(table, currentRow - 1, 9, 10);
        this.setCellText(basicInfoRow.getCell(9), exportData.getFlightDate(), false);

        // 第3行：气象信息标题行
        XWPFTableRow weatherTitleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, currentRow - 1, 0, 10);
        this.setCellText(weatherTitleRow.getCell(0), "气象信息", true, 10.5);

        // 第4行：始发地天气信息表头
        XWPFTableRow departureWeatherHeaderRow = table.getRow(currentRow++);
        this.setCellText(departureWeatherHeaderRow.getCell(0), "批次", true);
        this.setCellText(departureWeatherHeaderRow.getCell(1), "始发地", true);
        this.setCellText(departureWeatherHeaderRow.getCell(2), "天气", true);
        this.setCellText(departureWeatherHeaderRow.getCell(3), "云高(m)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(4), "温度(℃)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(5), "风向(°)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(6), "风速(m/s)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(7), "能见度(m)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(8), "QNH(hPa)", true);
        this.setCellText(departureWeatherHeaderRow.getCell(9), "正导航", true);
        this.setCellText(departureWeatherHeaderRow.getCell(10), "副导航", true);

        // 始发地天气信息数据行
        if (departureWeatherInfoList != null) {
            for (WordFlightWeatherInfoVo departureWeatherInfo : departureWeatherInfoList) {
                XWPFTableRow departureRow = table.getRow(currentRow++);
                this.setCellText(departureRow.getCell(0), String.valueOf(departureWeatherInfo.getBatch()), false);
                this.setCellText(departureRow.getCell(1), departureWeatherInfo.getLocationName(), false);
                this.setCellText(departureRow.getCell(2), departureWeatherInfo.getWeather(), false);
                this.setCellText(departureRow.getCell(3), departureWeatherInfo.getCloudHeight(), false);
                this.setCellText(departureRow.getCell(4), departureWeatherInfo.getTemperature(), false);
                this.setCellText(departureRow.getCell(5), departureWeatherInfo.getWindDirection(), false);
                this.setCellText(departureRow.getCell(6), departureWeatherInfo.getWindSpeed(), false);
                this.setCellText(departureRow.getCell(7), departureWeatherInfo.getVisibility(), false);
                this.setCellText(departureRow.getCell(8), departureWeatherInfo.getQnh(), false);
            }
        }

        // 目的地天气信息表头
        XWPFTableRow arrivalWeatherHeaderRow = table.getRow(currentRow++);
        this.setCellText(arrivalWeatherHeaderRow.getCell(0), "批次", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(1), "目的地", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(2), "天气", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(3), "云高(m)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(4), "温度(℃)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(5), "风向(°)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(6), "风速(m/s)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(7), "能见度(m)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(8), "QNH(hPa)", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(9), "正导航", true);
        this.setCellText(arrivalWeatherHeaderRow.getCell(10), "副导航", true);

        // 目的地天气信息数据行
        if (arrivalWeatherInfoList != null) {
            for (WordFlightWeatherInfoVo arrivalWeatherInfo : arrivalWeatherInfoList) {
                XWPFTableRow arrivalRow = table.getRow(currentRow++);
                this.setCellText(arrivalRow.getCell(0), String.valueOf(arrivalWeatherInfo.getBatch()), false);
                this.setCellText(arrivalRow.getCell(1), arrivalWeatherInfo.getLocationName(), false);
                this.setCellText(arrivalRow.getCell(2), arrivalWeatherInfo.getWeather(), false);
                this.setCellText(arrivalRow.getCell(3), arrivalWeatherInfo.getCloudHeight(), false);
                this.setCellText(arrivalRow.getCell(4), arrivalWeatherInfo.getTemperature(), false);
                this.setCellText(arrivalRow.getCell(5), arrivalWeatherInfo.getWindDirection(), false);
                this.setCellText(arrivalRow.getCell(6), arrivalWeatherInfo.getWindSpeed(), false);
                this.setCellText(arrivalRow.getCell(7), arrivalWeatherInfo.getVisibility(), false);
                this.setCellText(arrivalRow.getCell(8), arrivalWeatherInfo.getQnh(), false);
            }
        }

        // 动态信息标题行
        XWPFTableRow dynamicTitleRow = table.getRow(currentRow++);
        this.mergeCellsHorizontally(table, currentRow - 1, 0, 10);
        this.setCellText(dynamicTitleRow.getCell(0), "动态信息", true, 10.5);

        // 动态信息表头
        XWPFTableRow dynamicHeaderRow = table.getRow(currentRow++);
        this.setCellText(dynamicHeaderRow.getCell(0), "批次", true);
        this.setCellText(dynamicHeaderRow.getCell(1), "始发地", true);
        this.setCellText(dynamicHeaderRow.getCell(2), "目的地", true);
        this.setCellText(dynamicHeaderRow.getCell(3), "开车时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(4), "起飞时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(5), "着陆时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(6), "关车时刻", true);
        this.setCellText(dynamicHeaderRow.getCell(7), "地面时间(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(8), "空中时间(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(9), "时间小计(分钟)", true);
        this.setCellText(dynamicHeaderRow.getCell(10), "架次", true);

        // 动态信息数据行和统计
        if (dynamicInfoList != null) {
            for (WordFlightWeatherDynamicVo dynamicInfo : dynamicInfoList) {
                XWPFTableRow dataRow = table.getRow(currentRow++);

                this.setCellText(dataRow.getCell(0), String.valueOf(dynamicInfo.getBatch()), false);
                this.setCellText(dataRow.getCell(1), dynamicInfo.getDepartureLocation(), false);
                this.setCellText(dataRow.getCell(2), dynamicInfo.getArrivalLocation(), false);
                this.setCellText(dataRow.getCell(3), dynamicInfo.getCarStartTime(), false);
                this.setCellText(dataRow.getCell(4), dynamicInfo.getTakeOffTime(), false);
                this.setCellText(dataRow.getCell(5), dynamicInfo.getLandingTime(), false);
            }
        }

        // 合计行
        XWPFTableRow totalRow = table.getRow(currentRow);
        // 剩余油量：合并第1列和第2列
        this.mergeCellsHorizontally(table, currentRow, 0, 1);
        this.setCellText(totalRow.getCell(0), "剩余油量", true);

        // 剩余油量数据：合并第3列和第4列
        this.mergeCellsHorizontally(table, currentRow, 2, 3);
        this.setCellText(totalRow.getCell(2), "740L", false);

        // 总计：合并第5列、第6列、第7列
        this.mergeCellsHorizontally(table, currentRow, 4, 6);
        this.setCellText(totalRow.getCell(4), "总计", true);

        // 总计数据
        this.setCellText(totalRow.getCell(7), String.valueOf(exportData.getGroundTimeMinTotal()), false);
        this.setCellText(totalRow.getCell(8), String.valueOf(exportData.getAirTimeMinTotal()), false);
        this.setCellText(totalRow.getCell(9), String.valueOf(exportData.getTotalTimeMinTotal()), false);
        this.setCellText(totalRow.getCell(10), String.valueOf(exportData.getSortieCountTotal()), false);
    }

    /**
     * 水平合并单元格
     */
    private void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        for (int colIndex = fromCol; colIndex <= toCol; colIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(colIndex);
            if (colIndex == fromCol) {
                // 第一个单元格设置为重新开始
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // 其他单元格设置为继续
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置表格边框
     */
    private void setTableBorders(XWPFTable table) {
        table.getCTTbl().getTblPr().addNewTblBorders();
        // 这里可以根据需要设置更详细的边框样式
    }

    /**
     * 设置单元格文本
     */
    private void setCellText(XWPFTableCell cell, String text, boolean isBold) {
        this.setCellText(cell, text, isBold, 10.5);
    }

    /**
     * 设置单元格文本（带字体大小）
     */
    private void setCellText(XWPFTableCell cell, String text, boolean isBold, double fontSize) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun run = paragraph.createRun();
        run.setText(text != null ? text : "");
        run.setBold(isBold);
        run.setFontSize((int) fontSize);
        run.setFontFamily("宋体");

        // 设置单元格垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 设置页面格式
     */
    private void setPageFormat(XWPFDocument document) {
        try {
            // 获取文档的底层XML结构
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDocument1 ctDocument = document.getDocument();
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBody body = ctDocument.getBody();

            // 如果没有节属性，创建一个
            if (!body.isSetSectPr()) {
                body.addNewSectPr();
            }

            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr sectPr = body.getSectPr();

            // 设置页面大小和方向
            if (!sectPr.isSetPgSz()) {
                sectPr.addNewPgSz();
            }
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageSz pgSz = sectPr.getPgSz();

            // 设置为横向 (A4纸张横向)
            pgSz.setW(java.math.BigInteger.valueOf(16838)); // A4横向宽度 (11.69英寸)
            pgSz.setH(java.math.BigInteger.valueOf(11906)); // A4横向高度 (8.27英寸)
            pgSz.setOrient(org.openxmlformats.schemas.wordprocessingml.x2006.main.STPageOrientation.LANDSCAPE);

            // 设置页边距
            if (!sectPr.isSetPgMar()) {
                sectPr.addNewPgMar();
            }
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar pgMar = sectPr.getPgMar();

            // 页边距单位为twips (1英寸 = 1440 twips, 1厘米 ≈ 567 twips)
            pgMar.setTop(java.math.BigInteger.valueOf(964));   // 上边距 1.7cm = 1.7 * 567 = 964 twips
            pgMar.setBottom(java.math.BigInteger.valueOf(0));  // 下边距 0cm = 0 twips
            pgMar.setLeft(java.math.BigInteger.valueOf(953));  // 左边距 1.68cm = 1.68 * 567 = 953 twips
            pgMar.setRight(java.math.BigInteger.valueOf(924)); // 右边距 1.63cm = 1.68 * 567 = 924 twips
            log.info("设置页面格式成功");
        } catch (Exception e) {
            log.warn("设置页面格式失败", e);
        }
    }
}
