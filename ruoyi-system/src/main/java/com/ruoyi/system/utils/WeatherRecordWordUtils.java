package com.ruoyi.system.utils;

import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 气象信息记录页Word文档处理工具类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordWordUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordWordUtils.class);
    
    /**
     * 生成气象信息记录页Word文档
     *
     * @param templatePath 模板文件路径
     * @param data 气象信息数据
     * @param sealImagePath 公章图片路径
     * @return Word文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherRecordDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        logger.info("开始生成气象信息记录页Word文档 - 参考FlightTaskWordUtils方式");

        try (InputStream templateStream = Files.newInputStream(Paths.get(templatePath))) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);

            // 3. 填充表格数据
            fillTableData(document, data);

            // 4. 添加公章图片
            if (sealImagePath != null && new File(sealImagePath).exists()) {
                addSealImage(document, sealImagePath);
            }

            // 5. 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            logger.info("气象信息记录页Word文档生成完成");
            return outputStream.toByteArray();

        } catch (Exception e) {
            logger.error("生成气象信息记录页Word文档失败", e);
            throw new Exception("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建占位符参数
     */
    private static Map<String, String> buildPlaceholderParams(WordMeteorologicalVo data) {
        Map<String, String> params = new HashMap<>();

        // 基本信息占位符
        params.put("aircraftType", data.getAircraftType() != null ? data.getAircraftType() : "BELL429");
        params.put("registrationNumber", data.getRegistrationNumber() != null ? data.getRegistrationNumber() : "B-7613");
        params.put("flightDate", data.getFlightDate() != null ? data.getFlightDate() : "2025-05-20");

        // 合计信息占位符
        params.put("fuel", data.getFuel() != null ? data.getFuel() : "740b");
        params.put("groundTimeMinTotal", data.getGroundTimeMinTotal() != null ? data.getGroundTimeMinTotal() : "");
        params.put("airTimeMinTotal", data.getAirTimeMinTotal() != null ? data.getAirTimeMinTotal() : "");
        params.put("totalTimeMinTotal", data.getTotalTimeMinTotal() != null ? data.getTotalTimeMinTotal() : "");
        params.put("sortieCountTotal", data.getSortieCountTotal() != null ? data.getSortieCountTotal() : "");

        logger.debug("构建占位符参数完成，参数数量: {}", params.size());
        return params;
    }
    
    /**
     * 替换正文段落中的占位符
     */
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            replaceInParagraph(paragraph, params, true);
        }
    }

    /**
     * 替换段落和表格里的占位符
     */
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, params, false);
        }
        for (XWPFTable table : document.getTables()) {
            processTable(table, params);
        }
    }

    /**
     * 处理表格中的占位符
     */
    private static void processTable(XWPFTable table, Map<String, String> params) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    replaceInParagraph(paragraph, params, false);
                }
                // 嵌套表格递归处理
                for (XWPFTable nestedTable : cell.getTables()) {
                    processTable(nestedTable, params);
                }
            }
        }
    }

    /**
     * 替换段落中的占位符
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> params, boolean bold) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                if (text.contains(placeholder)) {
                    text = text.replace(placeholder, entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                // 只对替换后的内容设置样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(bold);
                run.setColor("000000");
            }
        }
    }

    /**
     * 填充表格数据
     */
    private static void fillTableData(XWPFDocument document, WordMeteorologicalVo data) {
        logger.info("开始填充表格数据");

        List<XWPFTable> tables = document.getTables();
        if (tables.isEmpty()) {
            logger.warn("文档中没有找到表格");
            return;
        }

        // 处理第一个表格（主表格）
        XWPFTable mainTable = tables.get(0);

        // 构建所有需要添加的数据行
        List<String[]> allDataRows = buildAllDataRows(data);

        // 在表格末尾追加数据行
        appendRows(mainTable, allDataRows);

        logger.info("表格数据填充完成，共添加{}行数据", allDataRows.size());
    }

    /**
     * 构建所有需要添加的数据行
     */
    private static List<String[]> buildAllDataRows(WordMeteorologicalVo data) {
        List<String[]> allRows = new ArrayList<>();

        // 1. 添加始发地气象信息数据
        if (data.getDepartureWeatherInfoList() != null && !data.getDepartureWeatherInfoList().isEmpty()) {
            for (WordFlightWeatherInfoVo weather : data.getDepartureWeatherInfoList()) {
                allRows.add(new String[]{
                    weather.getBatch() != null ? weather.getBatch() : "",
                    weather.getLocationName() != null ? weather.getLocationName() : "",
                    weather.getWeather() != null ? weather.getWeather() : "",
                    weather.getCloudHeight() != null ? weather.getCloudHeight() : "",
                    weather.getTemperature() != null ? weather.getTemperature() : "",
                    weather.getWindDirection() != null ? weather.getWindDirection() : "",
                    weather.getWindSpeed() != null ? weather.getWindSpeed() : "",
                    weather.getVisibility() != null ? weather.getVisibility() : "",
                    weather.getQnh() != null ? weather.getQnh() : "",
                    weather.getCrewName1() != null ? weather.getCrewName1() : (weather.getCrewRole1() != null ? weather.getCrewRole1() : ""),
                    weather.getCrewName2() != null ? weather.getCrewName2() : (weather.getCrewRole2() != null ? weather.getCrewRole2() : "")
                });
            }
        }

        // 2. 添加目的地气象信息数据
        if (data.getArrivalWeatherInfoList() != null && !data.getArrivalWeatherInfoList().isEmpty()) {
            for (WordFlightWeatherInfoVo weather : data.getArrivalWeatherInfoList()) {
                allRows.add(new String[]{
                    weather.getBatch() != null ? weather.getBatch() : "",
                    weather.getLocationName() != null ? weather.getLocationName() : "",
                    weather.getWeather() != null ? weather.getWeather() : "",
                    weather.getCloudHeight() != null ? weather.getCloudHeight() : "",
                    weather.getTemperature() != null ? weather.getTemperature() : "",
                    weather.getWindDirection() != null ? weather.getWindDirection() : "",
                    weather.getWindSpeed() != null ? weather.getWindSpeed() : "",
                    weather.getVisibility() != null ? weather.getVisibility() : "",
                    weather.getQnh() != null ? weather.getQnh() : "",
                    weather.getCrewName1() != null ? weather.getCrewName1() : (weather.getCrewRole1() != null ? weather.getCrewRole1() : ""),
                    weather.getCrewName2() != null ? weather.getCrewName2() : (weather.getCrewRole2() != null ? weather.getCrewRole2() : "")
                });
            }
        }

        // 3. 添加动态信息数据
        if (data.getDynamicInfoList() != null && !data.getDynamicInfoList().isEmpty()) {
            for (WordFlightWeatherDynamicVo dynamic : data.getDynamicInfoList()) {
                allRows.add(new String[]{
                    dynamic.getBatch() != null ? dynamic.getBatch() : "",
                    dynamic.getDepartureLocation() != null ? dynamic.getDepartureLocation() : "",
                    dynamic.getArrivalLocation() != null ? dynamic.getArrivalLocation() : "",
                    dynamic.getCarStartTime() != null ? dynamic.getCarStartTime() : "",
                    dynamic.getTakeOffTime() != null ? dynamic.getTakeOffTime() : "",
                    dynamic.getLandingTime() != null ? dynamic.getLandingTime() : "",
                    dynamic.getCarStopTime() != null ? dynamic.getCarStopTime() : "",
                    dynamic.getGroundTimeMin() != null ? dynamic.getGroundTimeMin() : "",
                    dynamic.getAirTimeMin() != null ? dynamic.getAirTimeMin() : "",
                    dynamic.getTotalTimeMin() != null ? dynamic.getTotalTimeMin() : "",
                    dynamic.getSortieCount() != null ? dynamic.getSortieCount() : ""
                });
            }
        }

        logger.debug("构建数据行完成，共{}行", allRows.size());
        return allRows;
    }

    /**
     * 新增多行到表格最后，按最后一行列数补齐（参考FlightTaskWordUtils）
     */
    private static void appendRows(XWPFTable table, List<String[]> dataRows) {
        List<XWPFTableRow> rows = table.getRows();
        XWPFTableRow lastRow = rows.get(rows.size() - 1);
        int columnCount = lastRow.getTableCells().size();

        for (String[] rowData : dataRows) {
            XWPFTableRow newRow = table.createRow();
            while (newRow.getTableCells().size() < columnCount) {
                newRow.addNewTableCell();
            }

            for (int j = 0; j < Math.min(columnCount, rowData.length); j++) {
                XWPFTableCell cell = newRow.getCell(j);

                // 设置垂直居中
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 获取单元格中的段落
                XWPFParagraph paragraph;
                if (cell.getParagraphs().isEmpty()) {
                    paragraph = cell.addParagraph();
                } else {
                    paragraph = cell.getParagraphs().get(0);
                }

                // 设置水平居中
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 新增 Run，只控制新写入文字的格式
                XWPFRun run = paragraph.createRun();
                run.setText(rowData[j]);

                // 只设置新写入的字体样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(false);
                run.setColor("000000");
            }
        }
    }
    
    /**
     * 在文档右下角添加公章图片
     */
    private static void addSealImage(XWPFDocument document, String imagePath) throws Exception {
        logger.debug("开始添加公章图片: {}", imagePath);

        try {
            // 在文档末尾添加段落用于放置公章
            XWPFParagraph sealParagraph = document.createParagraph();
            sealParagraph.setAlignment(ParagraphAlignment.RIGHT);

            XWPFRun sealRun = sealParagraph.createRun();

            // 读取图片文件
            try (InputStream imageStream = new FileInputStream(imagePath)) {
                // 确定图片格式
                int format = getImageFormat(imagePath);

                // 插入图片，设置合适的尺寸
                int width = Units.toEMU(80);   // 80像素宽度
                int height = Units.toEMU(80);  // 80像素高度

                sealRun.addPicture(imageStream, format, "seal", width, height);

                logger.debug("公章图片添加成功");
            }

        } catch (Exception e) {
            logger.error("添加公章图片失败: {}", imagePath, e);
            throw new Exception("添加公章图片失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据文件扩展名确定图片格式
     */
    private static int getImageFormat(String imagePath) {
        String extension = imagePath.toLowerCase();
        if (extension.endsWith(".png")) {
            return XWPFDocument.PICTURE_TYPE_PNG;
        } else if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
            return XWPFDocument.PICTURE_TYPE_JPEG;
        } else if (extension.endsWith(".gif")) {
            return XWPFDocument.PICTURE_TYPE_GIF;
        } else {
            return XWPFDocument.PICTURE_TYPE_PNG; // 默认PNG
        }
    }
}
