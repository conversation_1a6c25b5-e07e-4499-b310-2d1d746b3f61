package com.ruoyi.system.utils;

import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 气象信息记录页Word文档处理工具类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordWordUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordWordUtils.class);
    
    /**
     * 生成气象信息记录页Word文档
     *
     * @param templatePath 模板文件路径
     * @param data 气象信息数据
     * @param sealImagePath 公章图片路径
     * @return Word文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherRecordDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        logger.info("开始生成气象信息记录页Word文档 - 参考FlightTaskWordUtils方式");

        try (InputStream templateStream = Files.newInputStream(Paths.get(templatePath))) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);

            // 3. 填充表格数据
            fillTableData(document, data);

            // 4. 添加公章图片
            if (sealImagePath != null && new File(sealImagePath).exists()) {
                addSealImage(document, sealImagePath);
            }

            // 5. 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            logger.info("气象信息记录页Word文档生成完成");
            return outputStream.toByteArray();

        } catch (Exception e) {
            logger.error("生成气象信息记录页Word文档失败", e);
            throw new Exception("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建占位符参数
     */
    private static Map<String, String> buildPlaceholderParams(WordMeteorologicalVo data) {
        Map<String, String> params = new HashMap<>();

        // 基本信息占位符
        params.put("aircraftType", data.getAircraftType() != null ? data.getAircraftType() : "BELL429");
        params.put("registrationNumber", data.getRegistrationNumber() != null ? data.getRegistrationNumber() : "B-7613");
        params.put("flightDate", data.getFlightDate() != null ? data.getFlightDate() : "2025-05-20");

        // 合计信息占位符
        params.put("fuel", data.getFuel() != null ? data.getFuel() : "740b");
        params.put("groundTimeMinTotal", data.getGroundTimeMinTotal() != null ? data.getGroundTimeMinTotal() : "");
        params.put("airTimeMinTotal", data.getAirTimeMinTotal() != null ? data.getAirTimeMinTotal() : "");
        params.put("totalTimeMinTotal", data.getTotalTimeMinTotal() != null ? data.getTotalTimeMinTotal() : "");
        params.put("sortieCountTotal", data.getSortieCountTotal() != null ? data.getSortieCountTotal() : "");

        logger.debug("构建占位符参数完成，参数数量: {}", params.size());
        return params;
    }
    
    /**
     * 替换正文段落中的占位符
     */
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            replaceInParagraph(paragraph, params, true);
        }
    }

    /**
     * 替换段落和表格里的占位符
     */
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, params, false);
        }
        for (XWPFTable table : document.getTables()) {
            processTable(table, params);
        }
    }

    /**
     * 处理表格中的占位符
     */
    private static void processTable(XWPFTable table, Map<String, String> params) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    replaceInParagraph(paragraph, params, false);
                }
                // 嵌套表格递归处理
                for (XWPFTable nestedTable : cell.getTables()) {
                    processTable(nestedTable, params);
                }
            }
        }
    }

    /**
     * 替换段落中的占位符
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> params, boolean bold) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                if (text.contains(placeholder)) {
                    text = text.replace(placeholder, entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                // 只对替换后的内容设置样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(bold);
                run.setColor("000000");
            }
        }
    }

    /**
     * 填充表格数据 - 根据表头定位填充
     */
    private static void fillTableData(XWPFDocument document, WordMeteorologicalVo data) {
        logger.info("开始填充表格数据 - 根据表头定位");

        List<XWPFTable> tables = document.getTables();
        if (tables.isEmpty()) {
            logger.warn("文档中没有找到表格");
            return;
        }

        // 处理第一个表格（主表格）
        XWPFTable mainTable = tables.get(0);

        // 分析表格结构，找到各个表头的位置
        TableHeaderInfo headerInfo = analyzeTableHeaders(mainTable);

        // 根据表头位置分别填充数据
        fillDataByHeaders(mainTable, headerInfo, data);

        logger.info("表格数据填充完成");
    }

    /**
     * 表头信息结构
     */
    private static class TableHeaderInfo {
        int departureWeatherHeaderRow = -1;  // 始发地气象信息表头行
        int arrivalWeatherHeaderRow = -1;    // 目的地气象信息表头行
        int dynamicInfoHeaderRow = -1;       // 动态信息表头行
        int totalRow = -1;                   // 合计行
    }

    /**
     * 分析表格表头结构
     */
    private static TableHeaderInfo analyzeTableHeaders(XWPFTable table) {
        TableHeaderInfo headerInfo = new TableHeaderInfo();
        List<XWPFTableRow> rows = table.getRows();

        logger.info("=== 开始分析表格表头结构 ===");
        logger.info("表格总行数: {}", rows.size());

        for (int i = 0; i < rows.size(); i++) {
            String rowText = getRowText(rows.get(i));
            logger.info("第{}行内容: [{}]", i, rowText);

            // 查找始发地气象信息表头
            if (headerInfo.departureWeatherHeaderRow == -1 &&
                rowText.contains("始发地") && rowText.contains("天气") && !rowText.contains("目的地")) {
                headerInfo.departureWeatherHeaderRow = i;
                logger.info("找到始发地气象信息表头: 第{}行", i);
            }

            // 查找目的地气象信息表头
            if (headerInfo.arrivalWeatherHeaderRow == -1 &&
                rowText.contains("目的地") && rowText.contains("天气")) {
                headerInfo.arrivalWeatherHeaderRow = i;
                logger.info("找到目的地气象信息表头: 第{}行", i);
            }

            // 查找动态信息表头
            if (headerInfo.dynamicInfoHeaderRow == -1) {
                // 多种识别方式
                boolean isDynamicHeader = false;

                // 方式1：包含"开车时刻"
                if (rowText.contains("开车时刻")) {
                    isDynamicHeader = true;
                    logger.debug("通过'开车时刻'识别动态信息表头");
                }

                // 方式2：包含"始发地"和"目的地"和时间相关字段
                if (!isDynamicHeader && rowText.contains("始发地") && rowText.contains("目的地") &&
                    (rowText.contains("开车") || rowText.contains("起飞") || rowText.contains("时刻"))) {
                    isDynamicHeader = true;
                    logger.debug("通过'始发地+目的地+时间'识别动态信息表头");
                }

                // 方式3：包含"动态信息"标题后的第一个表头
                if (!isDynamicHeader && rowText.contains("动态信息")) {
                    // 这是动态信息标题行，下一行可能是表头
                    logger.debug("发现动态信息标题行: 第{}行", i);
                }

                // 方式4：包含多个时间字段
                if (!isDynamicHeader && rowText.contains("起飞") && rowText.contains("着陆")) {
                    isDynamicHeader = true;
                    logger.debug("通过'起飞+着陆'识别动态信息表头");
                }

                if (isDynamicHeader) {
                    headerInfo.dynamicInfoHeaderRow = i;
                    logger.info("找到动态信息表头: 第{}行, 内容: {}", i, rowText);
                }
            }

            // 查找合计行
            if (headerInfo.totalRow == -1 &&
                (rowText.contains("剩余油量") || rowText.contains("合计"))) {
                headerInfo.totalRow = i;
                logger.info("找到合计行: 第{}行", i);
            }
        }

        // 输出分析结果总结
        logger.info("=== 表头分析结果总结 ===");
        logger.info("始发地气象信息表头: 第{}行", headerInfo.departureWeatherHeaderRow);
        logger.info("目的地气象信息表头: 第{}行", headerInfo.arrivalWeatherHeaderRow);
        logger.info("动态信息表头: 第{}行", headerInfo.dynamicInfoHeaderRow);
        logger.info("合计行: 第{}行", headerInfo.totalRow);
        logger.info("=== 表头分析完成 ===");

        return headerInfo;
    }

    /**
     * 获取行的所有文本
     */
    private static String getRowText(XWPFTableRow row) {
        StringBuilder text = new StringBuilder();
        for (XWPFTableCell cell : row.getTableCells()) {
            text.append(cell.getText()).append(" ");
        }
        return text.toString();
    }

    /**
     * 根据表头位置分别填充数据
     */
    private static void fillDataByHeaders(XWPFTable table, TableHeaderInfo headerInfo, WordMeteorologicalVo data) {
        logger.info("开始根据表头位置分别填充数据");

        // 1. 填充始发地气象信息数据
        if (headerInfo.departureWeatherHeaderRow != -1 &&
            data.getDepartureWeatherInfoList() != null && !data.getDepartureWeatherInfoList().isEmpty()) {

            int insertPosition = headerInfo.departureWeatherHeaderRow + 1;
            logger.info("=== 填充始发地气象信息数据 ===");
            logger.info("表头行: {}, 插入位置: {}, 数据条数: {}",
                       headerInfo.departureWeatherHeaderRow, insertPosition, data.getDepartureWeatherInfoList().size());

            for (int i = 0; i < data.getDepartureWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getDepartureWeatherInfoList().get(i);
                logger.info("处理始发地数据第{}条: 批次={}, 位置={}, 天气={}",
                           i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition, rowData);
                insertPosition++; // 下一行插入位置
            }
        } else {
            logger.warn("始发地气象信息数据为空或表头未找到");
        }

        // 2. 填充目的地气象信息数据
        if (headerInfo.arrivalWeatherHeaderRow != -1 &&
            data.getArrivalWeatherInfoList() != null && !data.getArrivalWeatherInfoList().isEmpty()) {

            int insertPosition = headerInfo.arrivalWeatherHeaderRow + 1;
            logger.info("=== 填充目的地气象信息数据 ===");
            logger.info("表头行: {}, 插入位置: {}, 数据条数: {}",
                       headerInfo.arrivalWeatherHeaderRow, insertPosition, data.getArrivalWeatherInfoList().size());

            for (int i = 0; i < data.getArrivalWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getArrivalWeatherInfoList().get(i);
                logger.info("处理目的地数据第{}条: 批次={}, 位置={}, 天气={}",
                           i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition, rowData);
                insertPosition++; // 下一行插入位置
            }
        } else {
            logger.warn("目的地气象信息数据为空或表头未找到");
        }

        // 3. 填充动态信息数据
        if (headerInfo.dynamicInfoHeaderRow != -1 &&
            data.getDynamicInfoList() != null && !data.getDynamicInfoList().isEmpty()) {

            int insertPosition = headerInfo.dynamicInfoHeaderRow + 1;
            logger.info("=== 填充动态信息数据 ===");
            logger.info("表头行: {}, 插入位置: {}, 数据条数: {}",
                       headerInfo.dynamicInfoHeaderRow, insertPosition, data.getDynamicInfoList().size());

            for (int i = 0; i < data.getDynamicInfoList().size(); i++) {
                WordFlightWeatherDynamicVo dynamic = data.getDynamicInfoList().get(i);
                logger.info("处理动态信息第{}条: 批次={}, 始发地={}, 目的地={}, 开车时刻={}",
                           i + 1, dynamic.getBatch(), dynamic.getDepartureLocation(),
                           dynamic.getArrivalLocation(), dynamic.getCarStartTime());

                String[] rowData = buildDynamicRowData(dynamic);
                insertRowAtPosition(table, insertPosition, rowData);
                insertPosition++; // 下一行插入位置
            }
        } else {
            logger.warn("动态信息数据为空或表头未找到");
        }

        logger.info("根据表头位置填充数据完成");
    }

    /**
     * 构建气象信息行数据
     */
    private static String[] buildWeatherRowData(WordFlightWeatherInfoVo weather) {
        // 根据表格列数构建数据数组，确保有足够的列
        String[] rowData = new String[]{
            weather.getBatch() != null ? weather.getBatch() : "",                    // 第1列：批次
            weather.getLocationName() != null ? weather.getLocationName() : "",      // 第2列：始发地/目的地
            weather.getWeather() != null ? weather.getWeather() : "",                // 第3列：天气
            weather.getCloudHeight() != null ? weather.getCloudHeight() : "",        // 第4列：云高(m)
            weather.getTemperature() != null ? weather.getTemperature() : "",        // 第5列：温度(℃)
            weather.getWindDirection() != null ? weather.getWindDirection() : "",    // 第6列：风向(°)
            weather.getWindSpeed() != null ? weather.getWindSpeed() : "",            // 第7列：风速(m/s)
            weather.getVisibility() != null ? weather.getVisibility() : "",          // 第8列：能见度(m)
            weather.getQnh() != null ? weather.getQnh() : ""                         // 第9列：QNH(hPa)
        };

        logger.info("构建气象信息行数据 - 批次:{}, 位置:{}, 天气:{}, 云高:{}, 温度:{}, 风向:{}, 风速:{}, 能见度:{}, QNH:{}",
                   rowData[0], rowData[1], rowData[2], rowData[3], rowData[4], rowData[5], rowData[6], rowData[7], rowData[8]);
        return rowData;
    }

    /**
     * 构建动态信息行数据
     */
    private static String[] buildDynamicRowData(WordFlightWeatherDynamicVo dynamic) {
        // 根据动态信息表格列数构建数据数组
        String[] rowData = new String[]{
            dynamic.getBatch() != null ? dynamic.getBatch() : "",                           // 第1列：批次
            dynamic.getDepartureLocation() != null ? dynamic.getDepartureLocation() : "",  // 第2列：始发地
            dynamic.getArrivalLocation() != null ? dynamic.getArrivalLocation() : "",      // 第3列：目的地
            dynamic.getCarStartTime() != null ? dynamic.getCarStartTime() : "",            // 第4列：开车时刻
            dynamic.getTakeOffTime() != null ? dynamic.getTakeOffTime() : "",              // 第5列：起飞时刻
            dynamic.getLandingTime() != null ? dynamic.getLandingTime() : "",              // 第6列：着陆时刻
            dynamic.getCarStopTime() != null ? dynamic.getCarStopTime() : "",              // 第7列：关车时刻
            dynamic.getGroundTimeMin() != null ? dynamic.getGroundTimeMin() : "",          // 第8列：地面时间(分钟)
            dynamic.getAirTimeMin() != null ? dynamic.getAirTimeMin() : "",                // 第9列：空中时间(分钟)
            dynamic.getTotalTimeMin() != null ? dynamic.getTotalTimeMin() : "",            // 第10列：时间小计(分钟)
            dynamic.getSortieCount() != null ? dynamic.getSortieCount() : ""               // 第11列：架次
        };

        logger.info("构建动态信息行数据 - 批次:{}, 始发地:{}, 目的地:{}, 开车:{}, 起飞:{}, 着陆:{}, 关车:{}, 地面时间:{}, 空中时间:{}, 时间小计:{}, 架次:{}",
                   rowData[0], rowData[1], rowData[2], rowData[3], rowData[4], rowData[5], rowData[6], rowData[7], rowData[8], rowData[9], rowData[10]);
        return rowData;
    }

    /**
     * 在指定位置插入行
     */
    private static void insertRowAtPosition(XWPFTable table, int position, String[] rowData) {
        logger.info("在第{}行位置插入数据: [{}]", position, String.join(", ", rowData));

        // 插入新行
        XWPFTableRow newRow = table.insertNewTableRow(position);

        // 获取表格的列数（参考表头行）
        List<XWPFTableRow> rows = table.getRows();
        int columnCount = 0;
        if (!rows.isEmpty()) {
            // 查找一个有效的表头行来确定列数
            for (XWPFTableRow row : rows) {
                int cellCount = row.getTableCells().size();
                if (cellCount > columnCount) {
                    columnCount = cellCount;
                }
            }
            logger.debug("表格最大列数: {}", columnCount);
        }

        // 确保新行有足够的单元格
        while (newRow.getTableCells().size() < columnCount) {
            newRow.addNewTableCell();
        }
        logger.debug("新行创建完成，实际列数: {}", newRow.getTableCells().size());

        // 填充数据
        List<XWPFTableCell> cells = newRow.getTableCells();
        logger.info("开始填充数据 - 单元格数: {}, 数据长度: {}", cells.size(), rowData.length);

        for (int i = 0; i < cells.size(); i++) {
            XWPFTableCell cell = cells.get(i);
            String cellData = (i < rowData.length) ? rowData[i] : "";

            logger.debug("填充第{}列: [{}]", i, cellData);

            try {
                // 清除现有内容
                while (cell.getParagraphs().size() > 0) {
                    cell.removeParagraph(0);
                }

                // 设置垂直居中
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 创建新段落
                XWPFParagraph paragraph = cell.addParagraph();
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 创建新 Run 并设置文本
                XWPFRun run = paragraph.createRun();
                run.setText(cellData);

                // 设置字体样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(false);
                run.setColor("000000");

                logger.debug("第{}列填充成功: [{}]", i, cellData);

            } catch (Exception e) {
                logger.error("填充第{}列失败: {}", i, e.getMessage());
            }
        }

        logger.info("数据插入完成，共填充{}列", cells.size());
    }


    
    /**
     * 在文档右下角添加公章图片
     */
    private static void addSealImage(XWPFDocument document, String imagePath) throws Exception {
        logger.debug("开始添加公章图片: {}", imagePath);

        try {
            // 在文档末尾添加段落用于放置公章
            XWPFParagraph sealParagraph = document.createParagraph();
            sealParagraph.setAlignment(ParagraphAlignment.RIGHT);

            XWPFRun sealRun = sealParagraph.createRun();

            // 读取图片文件
            try (InputStream imageStream = new FileInputStream(imagePath)) {
                // 确定图片格式
                int format = getImageFormat(imagePath);

                // 插入图片，设置合适的尺寸
                int width = Units.toEMU(80);   // 80像素宽度
                int height = Units.toEMU(80);  // 80像素高度

                sealRun.addPicture(imageStream, format, "seal", width, height);

                logger.debug("公章图片添加成功");
            }

        } catch (Exception e) {
            logger.error("添加公章图片失败: {}", imagePath, e);
            throw new Exception("添加公章图片失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据文件扩展名确定图片格式
     */
    private static int getImageFormat(String imagePath) {
        String extension = imagePath.toLowerCase();
        if (extension.endsWith(".png")) {
            return XWPFDocument.PICTURE_TYPE_PNG;
        } else if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
            return XWPFDocument.PICTURE_TYPE_JPEG;
        } else if (extension.endsWith(".gif")) {
            return XWPFDocument.PICTURE_TYPE_GIF;
        } else {
            return XWPFDocument.PICTURE_TYPE_PNG; // 默认PNG
        }
    }
}
