# 气象信息记录页 - 模板实现方案

## 实现方式

参考 `FlightTaskWordUtils` 的实现方式：
1. **从模板文件读取** - 保留原有格式和表头
2. **占位符替换** - 替换基本信息
3. **表格末尾追加** - 添加数据行

## 核心特点

### ✅ 简单可靠
- 不重新定义表头
- 保持模板文件的原有结构
- 使用成熟的实现方式

### ✅ 占位符支持
```
${aircraftType}        - 机型
${registrationNumber}  - 注册号
${flightDate}         - 飞行日期
${fuel}               - 剩余油量
${groundTimeMinTotal} - 地面时间合计
${airTimeMinTotal}    - 空中时间合计
${totalTimeMinTotal}  - 时间小计合计
${sortieCountTotal}   - 架次合计
```

### ✅ 数据追加
- 在表格末尾自动追加数据行
- 按照表格列数自动补齐
- 统一的字体和格式设置

## 实现流程

### 1. 读取模板
```java
try (InputStream templateStream = Files.newInputStream(Paths.get(templatePath))) {
    XWPFDocument document = new XWPFDocument(templateStream);
    // ...
}
```

### 2. 构建占位符参数
```java
Map<String, String> params = new HashMap<>();
params.put("aircraftType", data.getAircraftType());
params.put("registrationNumber", data.getRegistrationNumber());
params.put("flightDate", data.getFlightDate());
// ...
```

### 3. 替换占位符
```java
// 替换文档中的占位符
replaceInDocumentBody(document, params);
processParagraphsAndTables(document, params);
```

### 4. 追加数据行
```java
// 构建所有需要添加的数据行
List<String[]> allDataRows = buildAllDataRows(data);

// 在表格末尾追加数据行
appendRows(mainTable, allDataRows);
```

## 数据行构建

### 始发地气象信息
```java
allRows.add(new String[]{
    weather.getBatch(),
    weather.getLocationName(),
    weather.getWeather(),
    weather.getCloudHeight(),
    weather.getTemperature(),
    weather.getWindDirection(),
    weather.getWindSpeed(),
    weather.getVisibility(),
    weather.getQnh(),
    weather.getCrewName1(),
    weather.getCrewName2()
});
```

### 目的地气象信息
```java
// 与始发地格式相同
allRows.add(new String[]{...});
```

### 动态信息
```java
allRows.add(new String[]{
    dynamic.getBatch(),
    dynamic.getDepartureLocation(),
    dynamic.getArrivalLocation(),
    dynamic.getCarStartTime(),
    dynamic.getTakeOffTime(),
    dynamic.getLandingTime(),
    dynamic.getCarStopTime(),
    dynamic.getGroundTimeMin(),
    dynamic.getAirTimeMin(),
    dynamic.getTotalTimeMin(),
    dynamic.getSortieCount()
});
```

## 模板文件要求

### 基本信息占位符
模板文件中应包含以下占位符：
- `${aircraftType}` - 机型位置
- `${registrationNumber}` - 注册号位置
- `${flightDate}` - 飞行日期位置

### 合计信息占位符
- `${fuel}` - 剩余油量位置
- `${groundTimeMinTotal}` - 地面时间合计位置
- `${airTimeMinTotal}` - 空中时间合计位置
- `${totalTimeMinTotal}` - 时间小计合计位置
- `${sortieCountTotal}` - 架次合计位置

### 表格结构
- 包含完整的表头定义
- 至少一行数据行作为格式参考
- 表格应该在文档的合适位置

## 立即测试

### 运行测试类
```java
WeatherRecordTemplateTest.main(new String[]{});
```

### 输出位置
```
D:/temp/weather_record_template_test/
└── weather_record_template_[时间戳].docx
```

## 与FlightTaskWordUtils的对比

### 相同点
- ✅ 从模板文件读取
- ✅ 使用占位符替换
- ✅ 在表格末尾追加数据
- ✅ 统一的字体格式设置

### 差异点
- **数据结构** - 适配气象信息的数据结构
- **占位符** - 定制化的占位符名称
- **数据行** - 包含始发地、目的地、动态信息

## 优势

### 1. 简单可靠
- 使用成熟的实现方式
- 代码结构清晰
- 易于理解和维护

### 2. 保持格式
- 不破坏模板文件的原有格式
- 保留表头和样式
- 确保文档的一致性

### 3. 灵活扩展
- 易于添加新的占位符
- 支持动态数量的数据行
- 可以轻松调整数据格式

## 文件结构

```
ruoyi-system/src/main/java/com/ruoyi/system/utils/
└── WeatherRecordWordUtils.java                    # 重新实现的工具类

ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/oc/
└── WeatherRecordTemplateTest.java                 # 模板测试类

ruoyi-admin/src/main/resources/word/
├── 气象信息模板1.docx                             # 模板文件
├── 公章.png                                       # 公章图片
└── 模板实现说明.md                                # 本说明文档
```

## 期望结果

生成的Word文档应该：
- ✅ 保持模板文件的原有格式
- ✅ 正确替换所有占位符
- ✅ 在表格末尾追加所有数据行
- ✅ 包含始发地气象信息数据
- ✅ 包含目的地气象信息数据
- ✅ 包含动态信息数据
- ✅ 显示公章图片

现在请运行 `WeatherRecordTemplateTest.main()` 来测试模板实现！
