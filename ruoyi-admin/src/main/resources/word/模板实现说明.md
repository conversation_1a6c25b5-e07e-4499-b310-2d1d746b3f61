# 气象信息记录页 - 基于表头的模板实现方案

## 实现方式

基于表头定位的精确填充方式：
1. **从模板文件读取** - 保留原有格式和表头
2. **占位符替换** - 替换基本信息
3. **智能表头识别** - 分析表格结构，识别三个表头位置
4. **精确位置插入** - 在对应表头后插入相应数据

## 核心特点

### ✅ 智能表头识别
- 自动识别始发地气象信息表头
- 自动识别目的地气象信息表头
- 自动识别动态信息表头
- 精确定位每个表头的位置

### ✅ 精确位置插入
- 始发地数据插入到始发地表头后
- 目的地数据插入到目的地表头后
- 动态信息数据插入到动态信息表头后
- 保持表格结构的完整性

### ✅ 占位符支持
```
${aircraftType}        - 机型
${registrationNumber}  - 注册号
${flightDate}         - 飞行日期
${fuel}               - 剩余油量
${groundTimeMinTotal} - 地面时间合计
${airTimeMinTotal}    - 空中时间合计
${totalTimeMinTotal}  - 时间小计合计
${sortieCountTotal}   - 架次合计
```

### ✅ 表格结构分析
- 分析表格中的三个表头部分
- 识别每个表头的具体位置
- 确保数据插入到正确位置

## 实现流程

### 1. 读取模板
```java
try (InputStream templateStream = Files.newInputStream(Paths.get(templatePath))) {
    XWPFDocument document = new XWPFDocument(templateStream);
    // ...
}
```

### 2. 构建占位符参数
```java
Map<String, String> params = new HashMap<>();
params.put("aircraftType", data.getAircraftType());
params.put("registrationNumber", data.getRegistrationNumber());
params.put("flightDate", data.getFlightDate());
// ...
```

### 3. 替换占位符
```java
// 替换文档中的占位符
replaceInDocumentBody(document, params);
processParagraphsAndTables(document, params);
```

### 4. 分析表格结构
```java
// 分析表格结构，找到各个表头的位置
TableHeaderInfo headerInfo = analyzeTableHeaders(mainTable);
```

### 5. 根据表头位置插入数据
```java
// 根据表头位置分别填充数据
fillDataByHeaders(mainTable, headerInfo, data);
```

## 数据行构建

### 始发地气象信息
```java
allRows.add(new String[]{
    weather.getBatch(),
    weather.getLocationName(),
    weather.getWeather(),
    weather.getCloudHeight(),
    weather.getTemperature(),
    weather.getWindDirection(),
    weather.getWindSpeed(),
    weather.getVisibility(),
    weather.getQnh(),
    weather.getCrewName1(),
    weather.getCrewName2()
});
```

### 目的地气象信息
```java
// 与始发地格式相同
allRows.add(new String[]{...});
```

### 动态信息
```java
allRows.add(new String[]{
    dynamic.getBatch(),
    dynamic.getDepartureLocation(),
    dynamic.getArrivalLocation(),
    dynamic.getCarStartTime(),
    dynamic.getTakeOffTime(),
    dynamic.getLandingTime(),
    dynamic.getCarStopTime(),
    dynamic.getGroundTimeMin(),
    dynamic.getAirTimeMin(),
    dynamic.getTotalTimeMin(),
    dynamic.getSortieCount()
});
```

## 模板文件要求

### 基本信息占位符
模板文件中应包含以下占位符：
- `${aircraftType}` - 机型位置
- `${registrationNumber}` - 注册号位置
- `${flightDate}` - 飞行日期位置

### 合计信息占位符
- `${fuel}` - 剩余油量位置
- `${groundTimeMinTotal}` - 地面时间合计位置
- `${airTimeMinTotal}` - 空中时间合计位置
- `${totalTimeMinTotal}` - 时间小计合计位置
- `${sortieCountTotal}` - 架次合计位置

### 表格结构
- 包含完整的表头定义
- 至少一行数据行作为格式参考
- 表格应该在文档的合适位置

## 表头识别算法

### 识别逻辑
```java
// 查找始发地气象信息表头
if (rowText.contains("始发地") && rowText.contains("天气") && !rowText.contains("目的地")) {
    headerInfo.departureWeatherHeaderRow = i;
}

// 查找目的地气象信息表头
if (rowText.contains("目的地") && rowText.contains("天气")) {
    headerInfo.arrivalWeatherHeaderRow = i;
}

// 查找动态信息表头
if (rowText.contains("开车时刻") || (rowText.contains("始发地") && rowText.contains("目的地") && rowText.contains("开车"))) {
    headerInfo.dynamicInfoHeaderRow = i;
}
```

### 数据插入策略
```java
// 在始发地表头后插入始发地数据
int insertPosition = headerInfo.departureWeatherHeaderRow + 1;
insertRowAtPosition(table, insertPosition, weatherRowData);

// 在目的地表头后插入目的地数据
int insertPosition = headerInfo.arrivalWeatherHeaderRow + 1;
insertRowAtPosition(table, insertPosition, weatherRowData);

// 在动态信息表头后插入动态信息数据
int insertPosition = headerInfo.dynamicInfoHeaderRow + 1;
insertRowAtPosition(table, insertPosition, dynamicRowData);
```

## 立即测试

### 运行测试类
```java
WeatherRecordHeaderBasedTest.main(new String[]{});
```

### 输出位置
```
D:/temp/weather_record_header_based_test/
└── weather_record_header_based_[时间戳].docx
```

## 与之前实现的对比

### 之前的实现（表格末尾追加）
- ❌ 所有数据都追加到表格末尾
- ❌ 数据不在对应的表头下
- ❌ 表格结构混乱
- ❌ 不符合预期的文档格式

### 现在的实现（基于表头定位）
- ✅ 智能识别表头位置
- ✅ 数据插入到对应表头下
- ✅ 保持表格结构完整
- ✅ 符合预期的文档格式

### 技术改进
- **表头识别算法** - 自动识别三个表头位置
- **精确位置插入** - 在正确位置插入数据
- **结构保持** - 维护表格的逻辑结构
- **数据分组** - 按表头分组组织数据

## 优势

### 1. 简单可靠
- 使用成熟的实现方式
- 代码结构清晰
- 易于理解和维护

### 2. 保持格式
- 不破坏模板文件的原有格式
- 保留表头和样式
- 确保文档的一致性

### 3. 灵活扩展
- 易于添加新的占位符
- 支持动态数量的数据行
- 可以轻松调整数据格式

## 文件结构

```
ruoyi-system/src/main/java/com/ruoyi/system/utils/
└── WeatherRecordWordUtils.java                    # 基于表头的工具类

ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/oc/
└── WeatherRecordHeaderBasedTest.java              # 基于表头的测试类

ruoyi-admin/src/main/resources/word/
├── 气象信息模板1.docx                             # 模板文件
├── 公章.png                                       # 公章图片
└── 模板实现说明.md                                # 本说明文档
```

## 期望结果

生成的Word文档应该：
- ✅ 保持模板文件的原有格式
- ✅ 正确替换所有占位符
- ✅ 始发地数据出现在始发地表头下方
- ✅ 目的地数据出现在目的地表头下方
- ✅ 动态信息数据出现在动态信息表头下方
- ✅ 每个部分的数据都在正确的位置
- ✅ 显示公章图片

## 解决的核心问题

### 🎯 表格结构问题
- **问题**：模板中一个表格包含三个表头部分
- **解决**：智能识别每个表头位置，精确插入对应数据

### 🎯 数据位置问题
- **问题**：数据不在对应的表头下
- **解决**：根据表头位置分别插入相应数据

### 🎯 文档格式问题
- **问题**：表格结构混乱，不符合预期
- **解决**：保持表格逻辑结构，按表头分组

现在请运行 `WeatherRecordHeaderBasedTest.main()` 来测试基于表头的实现！
