package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.utils.WeatherRecordWordUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息记录页合并单元格测试类
 * 修复能见度和QNH列的合并问题
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordMergedCellTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordMergedCellTest.class);
    
    public static void main(String[] args) {
        WeatherRecordMergedCellTest test = new WeatherRecordMergedCellTest();
        test.runMergedCellTest();
    }
    
    /**
     * 运行合并单元格测试
     */
    public void runMergedCellTest() {
        logger.info("开始运行气象信息记录页合并单元格测试");
        
        try {
            // 1. 检查模板文件
            checkTemplateFile();
            
            // 2. 创建测试数据
            WordMeteorologicalVo testData = createMergedCellTestData();
            
            // 3. 生成文档
            byte[] documentBytes = generateDocument(testData);
            
            // 4. 保存文档
            String outputPath = saveDocument(documentBytes);
            
            // 5. 验证结果
            verifyDocument(outputPath, documentBytes.length);
            
            logger.info("合并单元格测试完成！");
            printSuccessInfo(outputPath);
            
        } catch (Exception e) {
            logger.error("合并单元格测试失败", e);
            printErrorInfo(e);
        }
    }
    
    /**
     * 检查模板文件
     */
    private void checkTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("模板文件不存在: " + templatePath);
        }
        
        logger.info("模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建合并单元格测试数据
     */
    private WordMeteorologicalVo createMergedCellTestData() {
        logger.info("创建合并单元格测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo departureWeather = new WordFlightWeatherInfoVo();
        departureWeather.setBatch("1");
        departureWeather.setLocationName("星野");
        departureWeather.setWeather("晴天");
        departureWeather.setCloudHeight("无影响");
        departureWeather.setTemperature("26");
        departureWeather.setWindDirection("160");
        departureWeather.setWindSpeed("4");
        departureWeather.setVisibility("9999");
        departureWeather.setQnh("1004");
        departureWeatherList.add(departureWeather);
        data.setDepartureWeatherInfoList(departureWeatherList);
        
        // 目的地气象信息
        List<WordFlightWeatherInfoVo> arrivalWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo arrivalWeather = new WordFlightWeatherInfoVo();
        arrivalWeather.setBatch("1");
        arrivalWeather.setLocationName("星野");
        arrivalWeather.setWeather("晴天");
        arrivalWeather.setCloudHeight("无影响");
        arrivalWeather.setTemperature("26");
        arrivalWeather.setWindDirection("160");
        arrivalWeather.setWindSpeed("4");
        arrivalWeather.setVisibility("9999");
        arrivalWeather.setQnh("1004");
        arrivalWeatherList.add(arrivalWeather);
        data.setArrivalWeatherInfoList(arrivalWeatherList);
        
        // 动态信息
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        
        // 第一行动态数据
        WordFlightWeatherDynamicVo dynamic1 = new WordFlightWeatherDynamicVo();
        dynamic1.setBatch("1");
        dynamic1.setDepartureLocation("星野");
        dynamic1.setArrivalLocation("星野");
        dynamic1.setCarStartTime("19:43");
        dynamic1.setTakeOffTime("19:47");
        dynamic1.setLandingTime("20:03");
        dynamic1.setCarStopTime("20:05");
        dynamic1.setGroundTimeMin("6");
        dynamic1.setAirTimeMin("16");
        dynamic1.setTotalTimeMin("22");
        dynamic1.setSortieCount("1");
        dynamicInfoList.add(dynamic1);
        
        // 第二行动态数据
        WordFlightWeatherDynamicVo dynamic2 = new WordFlightWeatherDynamicVo();
        dynamic2.setBatch("2");
        dynamic2.setDepartureLocation("星野");
        dynamic2.setArrivalLocation("星野");
        dynamic2.setCarStartTime("20:37");
        dynamic2.setTakeOffTime("20:40");
        dynamic2.setLandingTime("21:15");
        dynamic2.setCarStopTime("21:17");
        dynamic2.setGroundTimeMin("5");
        dynamic2.setAirTimeMin("35");
        dynamic2.setTotalTimeMin("40");
        dynamic2.setSortieCount("2");
        dynamicInfoList.add(dynamic2);
        
        data.setDynamicInfoList(dynamicInfoList);
        
        logger.info("合并单元格测试数据创建完成");
        return data;
    }
    
    /**
     * 生成文档
     */
    private byte[] generateDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成Word文档 - 合并单元格版本");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherRecordWordUtils.generateWeatherRecordDocument(templatePath, data, sealPath);
        
        logger.info("Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private String saveDocument(byte[] documentBytes) throws IOException {
        String outputDir = "D:/temp/weather_record_merged_cell_test/";
        
        // 创建输出目录
        Path dirPath = Paths.get(outputDir);
        try {
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("创建输出目录: {}", outputDir);
            }
        } catch (IOException e) {
            logger.warn("无法创建目录 {}, 使用当前目录", outputDir);
            outputDir = "./";
        }
        
        String fileName = "weather_record_merged_cell_" + System.currentTimeMillis() + ".docx";
        String outputPath = outputDir + fileName;
        
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 验证文档
     */
    private void verifyDocument(String outputPath, int documentSize) throws Exception {
        logger.info("验证生成的文档...");
        
        // 检查文件是否存在
        Path filePath = Paths.get(outputPath);
        if (!Files.exists(filePath)) {
            throw new Exception("文档文件不存在: " + outputPath);
        }
        
        // 检查文件大小
        try {
            long fileSize = Files.size(filePath);
            logger.info("文件大小验证: 内存中 {} 字节, 磁盘上 {} 字节", documentSize, fileSize);
            
            if (fileSize < 1024) {
                throw new Exception("文档文件可能太小，可能生成有问题");
            }
            
            logger.info("文档验证通过");
            
        } catch (IOException e) {
            throw new Exception("无法获取文件大小", e);
        }
    }
    
    /**
     * 打印成功信息
     */
    private void printSuccessInfo(String outputPath) {
        System.out.println("\n=== 📋 合并单元格测试成功完成 📋 ===");
        System.out.println();
        System.out.println("生成的文档位置：" + outputPath);
        System.out.println();
        System.out.println("🔧 合并单元格修复内容：");
        System.out.println("✓ 将能见度和QNH合并为一列");
        System.out.println("✓ 调整数据数组长度为8列");
        System.out.println("✓ 格式：能见度/QNH（如：9999/1004）");
        System.out.println("✓ 确保数据对齐到正确列");
        System.out.println();
        System.out.println("📊 气象信息表格结构（8列）：");
        System.out.println("1. 批次");
        System.out.println("2. 始发地/目的地");
        System.out.println("3. 天气");
        System.out.println("4. 云高(m)");
        System.out.println("5. 温度(℃)");
        System.out.println("6. 风向(°)");
        System.out.println("7. 风速(m/s)");
        System.out.println("8. 能见度(m)/QNH(hPa)");
        System.out.println();
        System.out.println("🎯 期望结果：");
        System.out.println("- ✅ 始发地数据：在始发地表头下方，8列完整显示");
        System.out.println("- ✅ 目的地数据：在目的地表头下方，8列完整显示");
        System.out.println("- ✅ 动态信息数据：在动态信息表头下方，11列完整显示");
        System.out.println("- ✅ 能见度/QNH：合并显示为 9999/1004 格式");
        System.out.println("- ✅ 数据位置：所有数据对齐到正确列");
        System.out.println();
        System.out.println("🔍 验证要点：");
        System.out.println("1. 检查控制台日志中的数据构建过程");
        System.out.println("2. 确认能见度/QNH是否合并显示");
        System.out.println("3. 验证数据是否对齐到正确列");
        System.out.println("4. 检查表格结构是否完整");
        System.out.println();
        System.out.println("请打开生成的Word文档验证合并单元格修复结果！");
        System.out.println("现在能见度和QNH应该合并为一列，数据位置应该正确对齐。");
        System.out.println();
    }
    
    /**
     * 打印错误信息
     */
    private void printErrorInfo(Exception e) {
        System.err.println("\n=== ❌ 合并单元格测试失败 ===");
        System.err.println();
        System.err.println("错误信息: " + e.getMessage());
        System.err.println();
        System.err.println("调试建议：");
        System.err.println("1. 检查控制台日志中的数据构建过程");
        System.err.println("2. 确认表格列数是否匹配");
        System.err.println("3. 验证数据数组长度是否正确");
        System.err.println("4. 检查合并单元格逻辑是否正确");
        System.err.println();
    }
}
