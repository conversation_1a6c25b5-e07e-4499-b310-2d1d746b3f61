package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.utils.WeatherRecordWordUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息记录页数据调试测试类
 * 专门调试数据填充问题
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordDataDebugTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordDataDebugTest.class);
    
    public static void main(String[] args) {
        WeatherRecordDataDebugTest test = new WeatherRecordDataDebugTest();
        test.runDataDebugTest();
    }
    
    /**
     * 运行数据调试测试
     */
    public void runDataDebugTest() {
        logger.info("开始运行气象信息记录页数据调试测试");
        
        try {
            // 1. 检查模板文件
            checkTemplateFile();
            
            // 2. 创建详细测试数据
            WordMeteorologicalVo testData = createDetailedTestData();
            
            // 3. 打印测试数据详情
            printTestDataDetails(testData);
            
            // 4. 生成文档
            byte[] documentBytes = generateDocument(testData);
            
            // 5. 保存文档
            String outputPath = saveDocument(documentBytes);
            
            // 6. 验证结果
            verifyDocument(outputPath, documentBytes.length);
            
            logger.info("数据调试测试完成！");
            printSuccessInfo(outputPath);
            
        } catch (Exception e) {
            logger.error("数据调试测试失败", e);
            printErrorInfo(e);
        }
    }
    
    /**
     * 检查模板文件
     */
    private void checkTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("模板文件不存在: " + templatePath);
        }
        
        logger.info("模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建详细测试数据
     */
    private WordMeteorologicalVo createDetailedTestData() {
        logger.info("创建详细测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo departureWeather = new WordFlightWeatherInfoVo();
        departureWeather.setBatch("1");
        departureWeather.setLocationName("星野");
        departureWeather.setWeather("晴天");
        departureWeather.setCloudHeight("无影响");
        departureWeather.setTemperature("26");
        departureWeather.setWindDirection("160");
        departureWeather.setWindSpeed("4");
        departureWeather.setVisibility("9999");
        departureWeather.setQnh("1004");
        departureWeather.setCrewName1("徐建军");
        departureWeather.setCrewName2("王折");
        departureWeatherList.add(departureWeather);
        data.setDepartureWeatherInfoList(departureWeatherList);
        
        // 目的地气象信息
        List<WordFlightWeatherInfoVo> arrivalWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo arrivalWeather = new WordFlightWeatherInfoVo();
        arrivalWeather.setBatch("1");
        arrivalWeather.setLocationName("星野");
        arrivalWeather.setWeather("晴天");
        arrivalWeather.setCloudHeight("无影响");
        arrivalWeather.setTemperature("26");
        arrivalWeather.setWindDirection("160");
        arrivalWeather.setWindSpeed("4");
        arrivalWeather.setVisibility("9999");
        arrivalWeather.setQnh("1004");
        arrivalWeather.setCrewName1("徐建军");
        arrivalWeather.setCrewName2("王折");
        arrivalWeatherList.add(arrivalWeather);
        data.setArrivalWeatherInfoList(arrivalWeatherList);
        
        // 动态信息
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        
        // 第一行动态数据
        WordFlightWeatherDynamicVo dynamic1 = new WordFlightWeatherDynamicVo();
        dynamic1.setBatch("1");
        dynamic1.setDepartureLocation("星野");
        dynamic1.setArrivalLocation("星野");
        dynamic1.setCarStartTime("19:43");
        dynamic1.setTakeOffTime("19:47");
        dynamic1.setLandingTime("20:03");
        dynamic1.setCarStopTime("20:05");
        dynamic1.setGroundTimeMin("6");
        dynamic1.setAirTimeMin("16");
        dynamic1.setTotalTimeMin("22");
        dynamic1.setSortieCount("1");
        dynamicInfoList.add(dynamic1);
        
        // 第二行动态数据
        WordFlightWeatherDynamicVo dynamic2 = new WordFlightWeatherDynamicVo();
        dynamic2.setBatch("2");
        dynamic2.setDepartureLocation("星野");
        dynamic2.setArrivalLocation("星野");
        dynamic2.setCarStartTime("20:37");
        dynamic2.setTakeOffTime("20:40");
        dynamic2.setLandingTime("21:15");
        dynamic2.setCarStopTime("21:17");
        dynamic2.setGroundTimeMin("5");
        dynamic2.setAirTimeMin("35");
        dynamic2.setTotalTimeMin("40");
        dynamic2.setSortieCount("2");
        dynamicInfoList.add(dynamic2);
        
        data.setDynamicInfoList(dynamicInfoList);
        
        logger.info("详细测试数据创建完成");
        return data;
    }
    
    /**
     * 打印测试数据详情
     */
    private void printTestDataDetails(WordMeteorologicalVo data) {
        System.out.println("\n=== 📊 测试数据详情 ===");
        
        // 基本信息
        System.out.println("基本信息:");
        System.out.println("  机型: " + data.getAircraftType());
        System.out.println("  注册号: " + data.getRegistrationNumber());
        System.out.println("  飞行日期: " + data.getFlightDate());
        System.out.println("  剩余油量: " + data.getFuel());
        
        // 始发地气象信息
        System.out.println("\n始发地气象信息:");
        if (data.getDepartureWeatherInfoList() != null) {
            for (int i = 0; i < data.getDepartureWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getDepartureWeatherInfoList().get(i);
                System.out.println("  第" + (i + 1) + "条:");
                System.out.println("    批次: " + weather.getBatch());
                System.out.println("    位置: " + weather.getLocationName());
                System.out.println("    天气: " + weather.getWeather());
                System.out.println("    云高: " + weather.getCloudHeight());
                System.out.println("    温度: " + weather.getTemperature());
                System.out.println("    风向: " + weather.getWindDirection());
                System.out.println("    风速: " + weather.getWindSpeed());
                System.out.println("    能见度: " + weather.getVisibility());
                System.out.println("    QNH: " + weather.getQnh());
                System.out.println("    正常驾: " + weather.getCrewName1());
                System.out.println("    副驾驶: " + weather.getCrewName2());
            }
        }
        
        // 目的地气象信息
        System.out.println("\n目的地气象信息:");
        if (data.getArrivalWeatherInfoList() != null) {
            for (int i = 0; i < data.getArrivalWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getArrivalWeatherInfoList().get(i);
                System.out.println("  第" + (i + 1) + "条:");
                System.out.println("    批次: " + weather.getBatch());
                System.out.println("    位置: " + weather.getLocationName());
                System.out.println("    天气: " + weather.getWeather());
                System.out.println("    云高: " + weather.getCloudHeight());
                System.out.println("    温度: " + weather.getTemperature());
                System.out.println("    风向: " + weather.getWindDirection());
                System.out.println("    风速: " + weather.getWindSpeed());
                System.out.println("    能见度: " + weather.getVisibility());
                System.out.println("    QNH: " + weather.getQnh());
                System.out.println("    正常驾: " + weather.getCrewName1());
                System.out.println("    副驾驶: " + weather.getCrewName2());
            }
        }
        
        // 动态信息
        System.out.println("\n动态信息:");
        if (data.getDynamicInfoList() != null) {
            for (int i = 0; i < data.getDynamicInfoList().size(); i++) {
                WordFlightWeatherDynamicVo dynamic = data.getDynamicInfoList().get(i);
                System.out.println("  第" + (i + 1) + "条:");
                System.out.println("    批次: " + dynamic.getBatch());
                System.out.println("    始发地: " + dynamic.getDepartureLocation());
                System.out.println("    目的地: " + dynamic.getArrivalLocation());
                System.out.println("    开车时刻: " + dynamic.getCarStartTime());
                System.out.println("    起飞时刻: " + dynamic.getTakeOffTime());
                System.out.println("    着陆时刻: " + dynamic.getLandingTime());
                System.out.println("    关车时刻: " + dynamic.getCarStopTime());
                System.out.println("    地面时间: " + dynamic.getGroundTimeMin());
                System.out.println("    空中时间: " + dynamic.getAirTimeMin());
                System.out.println("    时间小计: " + dynamic.getTotalTimeMin());
                System.out.println("    架次: " + dynamic.getSortieCount());
            }
        }
        
        System.out.println("\n=== 测试数据详情结束 ===\n");
    }
    
    /**
     * 生成文档
     */
    private byte[] generateDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成Word文档 - 数据调试版本");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherRecordWordUtils.generateWeatherRecordDocument(templatePath, data, sealPath);
        
        logger.info("Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private String saveDocument(byte[] documentBytes) throws IOException {
        String outputDir = "D:/temp/weather_record_data_debug_test/";
        
        // 创建输出目录
        Path dirPath = Paths.get(outputDir);
        try {
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("创建输出目录: {}", outputDir);
            }
        } catch (IOException e) {
            logger.warn("无法创建目录 {}, 使用当前目录", outputDir);
            outputDir = "./";
        }
        
        String fileName = "weather_record_data_debug_" + System.currentTimeMillis() + ".docx";
        String outputPath = outputDir + fileName;
        
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 验证文档
     */
    private void verifyDocument(String outputPath, int documentSize) throws Exception {
        logger.info("验证生成的文档...");
        
        // 检查文件是否存在
        Path filePath = Paths.get(outputPath);
        if (!Files.exists(filePath)) {
            throw new Exception("文档文件不存在: " + outputPath);
        }
        
        // 检查文件大小
        try {
            long fileSize = Files.size(filePath);
            logger.info("文件大小验证: 内存中 {} 字节, 磁盘上 {} 字节", documentSize, fileSize);
            
            if (fileSize < 1024) {
                throw new Exception("文档文件可能太小，可能生成有问题");
            }
            
            logger.info("文档验证通过");
            
        } catch (IOException e) {
            throw new Exception("无法获取文件大小", e);
        }
    }
    
    /**
     * 打印成功信息
     */
    private void printSuccessInfo(String outputPath) {
        System.out.println("\n=== 🔍 数据调试测试成功完成 🔍 ===");
        System.out.println();
        System.out.println("生成的文档位置：" + outputPath);
        System.out.println();
        System.out.println("🔧 调试改进：");
        System.out.println("✓ 增加了详细的数据填充日志");
        System.out.println("✓ 改进了单元格文本设置方法");
        System.out.println("✓ 清除现有内容后重新填充");
        System.out.println("✓ 增强了数据构建过程的日志");
        System.out.println();
        System.out.println("📊 数据验证：");
        System.out.println("- 始发地气象信息：1条完整数据");
        System.out.println("- 目的地气象信息：1条完整数据");
        System.out.println("- 动态信息：2条完整数据");
        System.out.println();
        System.out.println("🎯 检查要点：");
        System.out.println("1. 查看控制台日志中的数据构建过程");
        System.out.println("2. 确认每个字段的数据是否正确");
        System.out.println("3. 检查Word文档中的数据填充结果");
        System.out.println("4. 对比控制台输出的测试数据详情");
        System.out.println();
        System.out.println("请打开生成的Word文档，并对比控制台输出的数据详情！");
        System.out.println("如果数据仍然不正确，请查看控制台日志中的详细调试信息。");
        System.out.println();
    }
    
    /**
     * 打印错误信息
     */
    private void printErrorInfo(Exception e) {
        System.err.println("\n=== ❌ 数据调试测试失败 ===");
        System.err.println();
        System.err.println("错误信息: " + e.getMessage());
        System.err.println();
        System.err.println("调试建议：");
        System.err.println("1. 检查控制台日志中的数据构建过程");
        System.err.println("2. 确认测试数据是否正确创建");
        System.err.println("3. 查看表头识别是否成功");
        System.err.println("4. 检查数据插入过程的日志");
        System.err.println();
    }
}
