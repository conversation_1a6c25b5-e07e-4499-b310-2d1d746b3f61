package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.oc.entity.OcAircraft;
import com.ruoyi.system.service.oc.IOcAircraftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 飞行器信息Controller
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Api("运控系统-飞行器管理")
@Validated
@RestController
@RequestMapping("/system/ocAircraft")
public class ocAircraftController extends BaseController {
    @Autowired
    private IOcAircraftService iOcAircraftService;

    /**
     * 查询飞行器的信息列表
     * 查询飞行器的信息列表
     */
    @ApiOperation("查询飞行器信息列表")
    @PreAuthorize("@ss.hasPermi('system:aircraft:list')")
    @GetMapping("/list")
    public TableDataInfo list(OcAircraft aircraft, @RequestHeader("CompanyCode") String companyCode) {
        startPage();
        List<OcAircraft> list = iOcAircraftService.selectAircraftList(aircraft, companyCode);
        //返回分页结果
        return getDataTable(list);
    }


}
