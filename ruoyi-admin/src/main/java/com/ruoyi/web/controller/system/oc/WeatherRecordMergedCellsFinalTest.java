package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.utils.WeatherRecordWordUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息记录页合并单元格最终测试类
 * 处理第8-9列合并（能见度），第10-11列合并（QNH）
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordMergedCellsFinalTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordMergedCellsFinalTest.class);
    
    public static void main(String[] args) {
        WeatherRecordMergedCellsFinalTest test = new WeatherRecordMergedCellsFinalTest();
        test.runMergedCellsFinalTest();
    }
    
    /**
     * 运行合并单元格最终测试
     */
    public void runMergedCellsFinalTest() {
        logger.info("开始运行气象信息记录页合并单元格最终测试");
        
        try {
            // 1. 检查模板文件
            checkTemplateFile();
            
            // 2. 创建测试数据
            WordMeteorologicalVo testData = createMergedCellsFinalTestData();
            
            // 3. 生成文档
            byte[] documentBytes = generateDocument(testData);
            
            // 4. 保存文档
            String outputPath = saveDocument(documentBytes);
            
            // 5. 验证结果
            verifyDocument(outputPath, documentBytes.length);
            
            logger.info("合并单元格最终测试完成！");
            printSuccessInfo(outputPath);
            
        } catch (Exception e) {
            logger.error("合并单元格最终测试失败", e);
            printErrorInfo(e);
        }
    }
    
    /**
     * 检查模板文件
     */
    private void checkTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("模板文件不存在: " + templatePath);
        }
        
        logger.info("模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建合并单元格最终测试数据
     */
    private WordMeteorologicalVo createMergedCellsFinalTestData() {
        logger.info("创建合并单元格最终测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo departureWeather = new WordFlightWeatherInfoVo();
        departureWeather.setBatch("1");
        departureWeather.setLocationName("星野");
        departureWeather.setWeather("晴天");
        departureWeather.setCloudHeight("无影响");
        departureWeather.setTemperature("26");
        departureWeather.setWindDirection("160");
        departureWeather.setWindSpeed("4");
        departureWeather.setVisibility("9999");
        departureWeather.setQnh("1004");
        departureWeatherList.add(departureWeather);
        data.setDepartureWeatherInfoList(departureWeatherList);
        
        // 目的地气象信息
        List<WordFlightWeatherInfoVo> arrivalWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo arrivalWeather = new WordFlightWeatherInfoVo();
        arrivalWeather.setBatch("1");
        arrivalWeather.setLocationName("星野");
        arrivalWeather.setWeather("晴天");
        arrivalWeather.setCloudHeight("无影响");
        arrivalWeather.setTemperature("26");
        arrivalWeather.setWindDirection("160");
        arrivalWeather.setWindSpeed("4");
        arrivalWeather.setVisibility("9999");
        arrivalWeather.setQnh("1004");
        arrivalWeatherList.add(arrivalWeather);
        data.setArrivalWeatherInfoList(arrivalWeatherList);
        
        // 动态信息
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        
        // 第一行动态数据
        WordFlightWeatherDynamicVo dynamic1 = new WordFlightWeatherDynamicVo();
        dynamic1.setBatch("1");
        dynamic1.setDepartureLocation("星野");
        dynamic1.setArrivalLocation("星野");
        dynamic1.setCarStartTime("19:43");
        dynamic1.setTakeOffTime("19:47");
        dynamic1.setLandingTime("20:03");
        dynamic1.setCarStopTime("20:05");
        dynamic1.setGroundTimeMin("6");
        dynamic1.setAirTimeMin("16");
        dynamic1.setTotalTimeMin("22");
        dynamic1.setSortieCount("1");
        dynamicInfoList.add(dynamic1);
        
        // 第二行动态数据
        WordFlightWeatherDynamicVo dynamic2 = new WordFlightWeatherDynamicVo();
        dynamic2.setBatch("2");
        dynamic2.setDepartureLocation("星野");
        dynamic2.setArrivalLocation("星野");
        dynamic2.setCarStartTime("20:37");
        dynamic2.setTakeOffTime("20:40");
        dynamic2.setLandingTime("21:15");
        dynamic2.setCarStopTime("21:17");
        dynamic2.setGroundTimeMin("5");
        dynamic2.setAirTimeMin("35");
        dynamic2.setTotalTimeMin("40");
        dynamic2.setSortieCount("2");
        dynamicInfoList.add(dynamic2);
        
        data.setDynamicInfoList(dynamicInfoList);
        
        logger.info("合并单元格最终测试数据创建完成");
        return data;
    }
    
    /**
     * 生成文档
     */
    private byte[] generateDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成Word文档 - 合并单元格最终版本");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板1.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherRecordWordUtils.generateWeatherRecordDocument(templatePath, data, sealPath);
        
        logger.info("Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private String saveDocument(byte[] documentBytes) throws IOException {
        String outputDir = "D:/temp/weather_record_merged_cells_final_test/";
        
        // 创建输出目录
        Path dirPath = Paths.get(outputDir);
        try {
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("创建输出目录: {}", outputDir);
            }
        } catch (IOException e) {
            logger.warn("无法创建目录 {}, 使用当前目录", outputDir);
            outputDir = "./";
        }
        
        String fileName = "weather_record_merged_cells_final_" + System.currentTimeMillis() + ".docx";
        String outputPath = outputDir + fileName;
        
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 验证文档
     */
    private void verifyDocument(String outputPath, int documentSize) throws Exception {
        logger.info("验证生成的文档...");
        
        // 检查文件是否存在
        Path filePath = Paths.get(outputPath);
        if (!Files.exists(filePath)) {
            throw new Exception("文档文件不存在: " + outputPath);
        }
        
        // 检查文件大小
        try {
            long fileSize = Files.size(filePath);
            logger.info("文件大小验证: 内存中 {} 字节, 磁盘上 {} 字节", documentSize, fileSize);
            
            if (fileSize < 1024) {
                throw new Exception("文档文件可能太小，可能生成有问题");
            }
            
            logger.info("文档验证通过");
            
        } catch (IOException e) {
            throw new Exception("无法获取文件大小", e);
        }
    }
    
    /**
     * 打印成功信息
     */
    private void printSuccessInfo(String outputPath) {
        System.out.println("\n=== 🎯 合并单元格最终测试完成 🎯 ===");
        System.out.println();
        System.out.println("生成的文档位置：" + outputPath);
        System.out.println();
        System.out.println("🔧 合并单元格最终修复内容：");
        System.out.println("✓ 调整数据数组长度为11列");
        System.out.println("✓ 第8列：能见度数据");
        System.out.println("✓ 第9列：能见度合并单元格的空列");
        System.out.println("✓ 第10列：QNH数据");
        System.out.println("✓ 第11列：QNH合并单元格的空列");
        System.out.println();
        System.out.println("📊 气象信息表格结构（11列-合并单元格版）：");
        System.out.println("1. 批次");
        System.out.println("2. 始发地/目的地");
        System.out.println("3. 天气");
        System.out.println("4. 云高(m)");
        System.out.println("5. 温度(℃)");
        System.out.println("6. 风向(°)");
        System.out.println("7. 风速(m/s)");
        System.out.println("8. 能见度(m) - 合并单元格第一列");
        System.out.println("9. 能见度(m) - 合并单元格第二列（空）");
        System.out.println("10. QNH(hPa) - 合并单元格第一列");
        System.out.println("11. QNH(hPa) - 合并单元格第二列（空）");
        System.out.println();
        System.out.println("🎯 期望结果：");
        System.out.println("- ✅ 能见度数据：显示在第8列，第9列为空");
        System.out.println("- ✅ QNH数据：显示在第10列，第11列为空");
        System.out.println("- ✅ 表头和数据：完全对齐");
        System.out.println("- ✅ 合并单元格：正确处理");
        System.out.println();
        System.out.println("🔍 验证要点：");
        System.out.println("1. 检查控制台中的11列数据构建日志");
        System.out.println("2. 确认能见度是否显示在第8列");
        System.out.println("3. 确认QNH是否显示在第10列");
        System.out.println("4. 验证第9列和第11列是否为空");
        System.out.println("5. 检查表头和数据是否完全对齐");
        System.out.println();
        System.out.println("这次应该能完美解决合并单元格的对齐问题！");
        System.out.println("请打开生成的Word文档验证最终修复结果！");
        System.out.println();
    }
    
    /**
     * 打印错误信息
     */
    private void printErrorInfo(Exception e) {
        System.err.println("\n=== ❌ 合并单元格最终测试失败 ===");
        System.err.println();
        System.err.println("错误信息: " + e.getMessage());
        System.err.println();
        System.err.println("调试建议：");
        System.err.println("1. 检查控制台中的11列数据构建日志");
        System.err.println("2. 确认表格列数是否为11列");
        System.err.println("3. 验证合并单元格处理逻辑");
        System.err.println("4. 检查数据数组长度");
        System.err.println();
    }
}
